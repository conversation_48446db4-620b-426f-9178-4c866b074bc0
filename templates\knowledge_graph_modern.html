{% extends "base_modern.html" %}

{% block title %}Knowledge Graph - Graphiti{% endblock %}

{% block extra_css %}
<style>
    #graph-container {
        height: 600px;
        border: 1px solid #dee2e6;
        border-radius: 12px;
        background: #f8f9fa;
    }
    
    .graph-controls {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1000;
    }
    
    .node-info-panel {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .relationship-badge {
        font-size: 0.75rem;
        margin: 2px;
    }
    
    .graph-stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Knowledge Graph Explorer</h1>
        <p class="text-muted">Visualize and explore entity relationships</p>
    </div>
    <div>
        <button class="btn btn-primary" onclick="refreshGraph()">
            <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
        <button class="btn btn-outline-success ms-2" onclick="exportGraph()">
            <i class="bi bi-download"></i> Export
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="toggleFullscreen()">
            <i class="bi bi-fullscreen"></i> Fullscreen
        </button>
    </div>
</div>

<!-- Graph Controls -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-sliders"></i> Graph Controls</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="entity-type-select" class="form-label">Entity Type</label>
                        <select class="form-select" id="entity-type-select">
                            <option value="">All Types</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="relationship-filter" class="form-label">Relationships</label>
                        <select class="form-select" id="relationship-filter">
                            <option value="">All Relationships</option>
                            <option value="RELATED_TO">Related To</option>
                            <option value="MENTIONED_IN">Mentioned In</option>
                            <option value="PART_OF">Part Of</option>
                            <option value="CAUSES">Causes</option>
                            <option value="TREATS">Treats</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="max-nodes" class="form-label">Max Nodes</label>
                        <input type="number" class="form-control" id="max-nodes" value="50" min="10" max="200">
                    </div>
                    <div class="col-md-3">
                        <label for="layout-select" class="form-label">Layout</label>
                        <select class="form-select" id="layout-select">
                            <option value="force">Force Directed</option>
                            <option value="hierarchical">Hierarchical</option>
                            <option value="circular">Circular</option>
                        </select>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control" id="node-search" placeholder="Search for entities...">
                            <button class="btn btn-outline-primary" onclick="searchNodes()">Search</button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-primary" onclick="loadGraph()">
                            <i class="bi bi-diagram-3"></i> Load Graph
                        </button>
                        <button class="btn btn-outline-secondary ms-2" onclick="clearGraph()">
                            <i class="bi bi-x-circle"></i> Clear
                        </button>
                        <button class="btn btn-outline-info ms-2" onclick="centerGraph()">
                            <i class="bi bi-bullseye"></i> Center
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Graph Stats -->
    <div class="col-md-4">
        <div class="card graph-stats-card">
            <div class="card-body text-center">
                <h5>Graph Statistics</h5>
                <div class="row">
                    <div class="col-6">
                        <div class="h3" id="nodes-count">-</div>
                        <small>Nodes</small>
                    </div>
                    <div class="col-6">
                        <div class="h3" id="edges-count">-</div>
                        <small>Edges</small>
                    </div>
                </div>
                <hr class="border-light">
                <div class="row">
                    <div class="col-6">
                        <div class="h5" id="entity-types-count">-</div>
                        <small>Entity Types</small>
                    </div>
                    <div class="col-6">
                        <div class="h5" id="relationship-types-count">-</div>
                        <small>Relationship Types</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Graph Visualization -->
<div class="row">
    <div class="col-md-9">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="bi bi-share"></i> Graph Visualization</h5>
                <div class="graph-controls">
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="zoomIn()">
                            <i class="bi bi-zoom-in"></i>
                        </button>
                        <button class="btn btn-outline-primary" onclick="zoomOut()">
                            <i class="bi bi-zoom-out"></i>
                        </button>
                        <button class="btn btn-outline-primary" onclick="fitGraph()">
                            <i class="bi bi-arrows-fullscreen"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0 position-relative">
                <!-- Loading State -->
                <div id="graph-loading" class="text-center py-5">
                    <div class="loading-spinner"></div>
                    <p class="text-muted mt-2">Loading knowledge graph...</p>
                </div>
                
                <!-- Graph Container -->
                <div id="graph-container" style="display: none;"></div>
                
                <!-- Empty State -->
                <div id="graph-empty" class="text-center py-5" style="display: none;">
                    <i class="bi bi-diagram-3 fs-1 text-muted mb-3"></i>
                    <h5>No Graph Data</h5>
                    <p class="text-muted">Load the graph to visualize entity relationships</p>
                    <button class="btn btn-primary" onclick="loadGraph()">
                        <i class="bi bi-diagram-3"></i> Load Graph
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Node Information Panel -->
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-info-circle"></i> Node Information</h6>
            </div>
            <div class="card-body node-info-panel">
                <div id="node-info-content">
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-mouse fs-1 mb-3"></i>
                        <p>Click on a node to see details</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Legend -->
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="bi bi-palette"></i> Legend</h6>
            </div>
            <div class="card-body">
                <div id="graph-legend">
                    <div class="mb-2">
                        <span class="badge bg-primary me-2">●</span>
                        <small>Person</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-success me-2">●</span>
                        <small>Organization</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-warning me-2">●</span>
                        <small>Location</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-danger me-2">●</span>
                        <small>Disease</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-info me-2">●</span>
                        <small>Medication</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-secondary me-2">●</span>
                        <small>Other</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Node Details Modal -->
<div class="modal fade" id="node-details-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Entity Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="node-details-content">
                <div class="text-center py-4">
                    <div class="loading-spinner"></div>
                    <p class="text-muted mt-2">Loading entity details...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="explore-node-btn">
                    <i class="bi bi-share"></i> Explore Connections
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
<script>
let network = null;
let nodes = new vis.DataSet([]);
let edges = new vis.DataSet([]);
let selectedNodeId = null;
let graphData = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeGraph();
    loadEntityTypes();
    setupEventListeners();
});

function setupEventListeners() {
    // Node search
    document.getElementById('node-search').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchNodes();
        }
    });
    
    // Layout change
    document.getElementById('layout-select').addEventListener('change', function() {
        if (network) {
            updateLayout();
        }
    });
}

function initializeGraph() {
    const container = document.getElementById('graph-container');
    
    const data = {
        nodes: nodes,
        edges: edges
    };
    
    const options = {
        nodes: {
            shape: 'dot',
            size: 20,
            font: {
                size: 12,
                color: '#333'
            },
            borderWidth: 2,
            shadow: true
        },
        edges: {
            width: 2,
            color: { inherit: 'from' },
            smooth: {
                type: 'continuous'
            },
            arrows: {
                to: { enabled: true, scaleFactor: 1 }
            },
            font: {
                size: 10,
                align: 'middle'
            }
        },
        physics: {
            enabled: true,
            stabilization: { iterations: 100 }
        },
        interaction: {
            hover: true,
            selectConnectedEdges: false
        }
    };
    
    network = new vis.Network(container, data, options);
    
    // Event listeners
    network.on('click', function(params) {
        if (params.nodes.length > 0) {
            const nodeId = params.nodes[0];
            selectNode(nodeId);
        }
    });
    
    network.on('doubleClick', function(params) {
        if (params.nodes.length > 0) {
            const nodeId = params.nodes[0];
            expandNode(nodeId);
        }
    });
    
    network.on('hoverNode', function(params) {
        showNodeTooltip(params.node);
    });
}

async function loadEntityTypes() {
    try {
        const response = await fetch('/api/entity-types');
        if (response.ok) {
            const data = await response.json();
            const select = document.getElementById('entity-type-select');
            select.innerHTML = '<option value="">All Types</option>';

            if (data.entity_types && Array.isArray(data.entity_types)) {
                data.entity_types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    select.appendChild(option);
                });
            }
        } else {
            console.warn('Failed to load entity types:', response.status);
        }
    } catch (error) {
        console.error('Error loading entity types:', error);
    }
}

async function loadGraph() {
    try {
        showGraphLoading();
        
        const entityType = document.getElementById('entity-type-select').value;
        const relationshipFilter = document.getElementById('relationship-filter').value;
        const maxNodes = document.getElementById('max-nodes').value;
        
        const params = new URLSearchParams({
            limit: maxNodes
        });
        
        if (entityType) params.append('entity_type', entityType);
        if (relationshipFilter) params.append('relationship_type', relationshipFilter);
        
        const response = await fetch(`/api/knowledge-graph/graph?${params}`);
        if (!response.ok) throw new Error('Failed to load graph');
        
        const data = await response.json();
        graphData = data;
        
        displayGraph(data);
        updateGraphStats(data);
        
    } catch (error) {
        console.error('Error loading graph:', error);
        showAlert('Error loading graph: ' + error.message, 'danger');
        showGraphEmpty();
    }
}

function displayGraph(data) {
    hideGraphLoading();

    if (!data.nodes || data.nodes.length === 0) {
        showGraphEmpty();
        return;
    }

    // Clear existing data
    nodes.clear();
    edges.clear();

    // Add nodes
    const nodeData = data.nodes.map(node => ({
        id: node.uuid,
        label: node.name || node.label || 'Unknown',
        title: `${node.type || 'Entity'}: ${node.name || 'Unknown'}`,
        color: getNodeColor(node.type),
        size: Math.max(15, Math.min(40, (node.mention_count || 1) * 2)),
        font: { size: Math.max(10, Math.min(16, (node.mention_count || 1) + 10)) },
        ...node
    }));

    // Add edges (relationships)
    const edgeData = (data.relationships || data.edges || []).map(edge => ({
        id: edge.id || `${edge.source}-${edge.target}`,
        from: edge.source,
        to: edge.target,
        label: edge.type || edge.relationship || '',
        title: `${edge.type || 'Relationship'}: ${edge.source_name || ''} → ${edge.target_name || ''}`,
        color: getEdgeColor(edge.type),
        ...edge
    }));

    nodes.add(nodeData);
    edges.add(edgeData);

    document.getElementById('graph-container').style.display = 'block';
    document.getElementById('graph-empty').style.display = 'none';

    // Fit the graph
    setTimeout(() => {
        if (network) {
            network.fit();
        }
    }, 500);
}

function getNodeColor(type) {
    const colorMap = {
        'Person': '#0d6efd',
        'Organization': '#198754',
        'Location': '#ffc107',
        'Disease': '#dc3545',
        'Medication': '#0dcaf0',
        'Nutrient': '#20c997',
        'Food': '#fd7e14',
        'Research': '#6f42c1',
        'Process': '#6c757d',
        'Symptom': '#e83e8c',
        'Treatment': '#198754'
    };
    
    return colorMap[type] || '#6c757d';
}

function getEdgeColor(type) {
    const colorMap = {
        'RELATED_TO': '#6c757d',
        'MENTIONED_IN': '#0d6efd',
        'PART_OF': '#198754',
        'CAUSES': '#dc3545',
        'TREATS': '#20c997'
    };
    
    return colorMap[type] || '#6c757d';
}

function updateGraphStats(data) {
    document.getElementById('nodes-count').textContent = data.nodes ? data.nodes.length : 0;
    document.getElementById('edges-count').textContent = (data.relationships || data.edges) ? (data.relationships || data.edges).length : 0;
    
    const entityTypes = new Set(data.nodes?.map(n => n.type) || []);
    const relationshipTypes = new Set(data.edges?.map(e => e.type) || []);
    
    document.getElementById('entity-types-count').textContent = entityTypes.size;
    document.getElementById('relationship-types-count').textContent = relationshipTypes.size;
}

async function selectNode(nodeId) {
    selectedNodeId = nodeId;
    
    try {
        const response = await fetch(`/api/entities/${nodeId}`);
        if (!response.ok) throw new Error('Failed to load node details');
        
        const entity = await response.json();
        displayNodeInfo(entity);
        
    } catch (error) {
        console.error('Error loading node details:', error);
        showAlert('Error loading node details: ' + error.message, 'danger');
    }
}

function displayNodeInfo(entity) {
    const content = document.getElementById('node-info-content');
    content.innerHTML = `
        <div class="text-center mb-3">
            <div class="mb-2">
                <span class="badge" style="background-color: ${getNodeColor(entity.type)}">${entity.type || 'Entity'}</span>
            </div>
            <h6>${entity.name || 'Unknown'}</h6>
        </div>
        
        <table class="table table-sm">
            <tr><td><strong>Mentions:</strong></td><td>${entity.mention_count || 0}</td></tr>
            <tr><td><strong>Confidence:</strong></td><td>${entity.confidence ? Math.round(entity.confidence * 100) + '%' : 'N/A'}</td></tr>
            <tr><td><strong>Created:</strong></td><td>${entity.created_at ? formatDate(entity.created_at) : 'Unknown'}</td></tr>
        </table>
        
        ${entity.description ? `
            <div class="mt-3">
                <h6>Description</h6>
                <p class="text-muted small">${entity.description}</p>
            </div>
        ` : ''}
        
        <div class="mt-3">
            <button class="btn btn-primary btn-sm w-100" onclick="viewNodeDetails('${entity.uuid}')">
                <i class="bi bi-eye"></i> View Full Details
            </button>
        </div>
    `;
}

async function viewNodeDetails(entityId) {
    try {
        const modal = new bootstrap.Modal(document.getElementById('node-details-modal'));
        modal.show();
        
        const response = await fetch(`/api/entities/${entityId}`);
        if (!response.ok) throw new Error('Failed to load entity details');
        
        const entity = await response.json();
        
        const content = document.getElementById('node-details-content');
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Basic Information</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Name:</strong></td><td>${entity.name || 'Unknown'}</td></tr>
                        <tr><td><strong>Type:</strong></td><td><span class="badge" style="background-color: ${getNodeColor(entity.type)}">${entity.type || 'Unknown'}</span></td></tr>
                        <tr><td><strong>Mentions:</strong></td><td>${entity.mention_count || 0}</td></tr>
                        <tr><td><strong>Confidence:</strong></td><td>${entity.confidence ? Math.round(entity.confidence * 100) + '%' : 'N/A'}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>Relationships</h6>
                    <div id="entity-relationships-list">
                        <div class="text-center py-3">
                            <div class="loading-spinner"></div>
                            <p class="text-muted mt-2">Loading relationships...</p>
                        </div>
                    </div>
                </div>
            </div>
            ${entity.description ? `
                <div class="mt-3">
                    <h6>Description</h6>
                    <p class="text-muted">${entity.description}</p>
                </div>
            ` : ''}
        `;
        
        // Load relationships
        loadEntityRelationships(entityId);
        
    } catch (error) {
        console.error('Error viewing node details:', error);
        showAlert('Error loading node details: ' + error.message, 'danger');
    }
}

async function loadEntityRelationships(entityId) {
    try {
        const response = await fetch(`/api/entities/${entityId}/relationships`);
        if (response.ok) {
            const data = await response.json();
            const relationships = data.relationships || [];
            
            const container = document.getElementById('entity-relationships-list');
            
            if (relationships.length > 0) {
                container.innerHTML = relationships.map(rel => `
                    <div class="mb-2">
                        <span class="badge relationship-badge" style="background-color: ${getEdgeColor(rel.type)}">${rel.type}</span>
                        <br>
                        <small>${rel.target_name}</small>
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<p class="text-muted">No relationships found</p>';
            }
        }
    } catch (error) {
        console.error('Error loading relationships:', error);
        document.getElementById('entity-relationships-list').innerHTML = '<p class="text-danger">Error loading relationships</p>';
    }
}

async function expandNode(nodeId) {
    try {
        const response = await fetch(`/api/knowledge-graph/expand/${nodeId}`);
        if (!response.ok) throw new Error('Failed to expand node');
        
        const data = await response.json();
        
        // Add new nodes and edges
        if (data.nodes) {
            const newNodes = data.nodes.filter(node => !nodes.get(node.id));
            const nodeData = newNodes.map(node => ({
                id: node.id,
                label: node.name || node.label || 'Unknown',
                title: `${node.type || 'Entity'}: ${node.name || 'Unknown'}`,
                color: getNodeColor(node.type),
                size: Math.max(15, Math.min(40, (node.mention_count || 1) * 2)),
                ...node
            }));
            nodes.add(nodeData);
        }
        
        if (data.edges) {
            const newEdges = data.edges.filter(edge => !edges.get(edge.id));
            const edgeData = newEdges.map(edge => ({
                id: edge.id,
                from: edge.source,
                to: edge.target,
                label: edge.type || edge.relationship || '',
                title: `${edge.type || 'Relationship'}: ${edge.source_name || ''} → ${edge.target_name || ''}`,
                color: getEdgeColor(edge.type),
                ...edge
            }));
            edges.add(edgeData);
        }
        
        showAlert(`Added ${data.nodes?.length || 0} nodes and ${data.edges?.length || 0} edges`, 'success', 2000);
        
    } catch (error) {
        console.error('Error expanding node:', error);
        showAlert('Error expanding node: ' + error.message, 'danger');
    }
}

function searchNodes() {
    const query = document.getElementById('node-search').value.toLowerCase();
    if (!query) return;
    
    const matchingNodes = nodes.get().filter(node => 
        node.label.toLowerCase().includes(query) || 
        (node.type && node.type.toLowerCase().includes(query))
    );
    
    if (matchingNodes.length > 0) {
        const nodeIds = matchingNodes.map(node => node.id);
        network.selectNodes(nodeIds);
        network.focus(nodeIds[0], { scale: 1.5 });
        showAlert(`Found ${matchingNodes.length} matching nodes`, 'success', 2000);
    } else {
        showAlert('No matching nodes found', 'warning', 2000);
    }
}

function updateLayout() {
    const layout = document.getElementById('layout-select').value;
    
    const options = {
        layout: {},
        physics: { enabled: true }
    };
    
    switch (layout) {
        case 'hierarchical':
            options.layout = {
                hierarchical: {
                    direction: 'UD',
                    sortMethod: 'directed'
                }
            };
            options.physics.enabled = false;
            break;
        case 'circular':
            options.layout = { randomSeed: 2 };
            options.physics.enabled = false;
            break;
        default: // force
            options.layout = { randomSeed: undefined };
            options.physics.enabled = true;
    }
    
    network.setOptions(options);
}

function clearGraph() {
    nodes.clear();
    edges.clear();
    document.getElementById('node-info-content').innerHTML = `
        <div class="text-center text-muted py-4">
            <i class="bi bi-mouse fs-1 mb-3"></i>
            <p>Click on a node to see details</p>
        </div>
    `;
    updateGraphStats({ nodes: [], edges: [] });
}

function centerGraph() {
    if (network) {
        network.fit();
    }
}

function zoomIn() {
    if (network) {
        const scale = network.getScale() * 1.2;
        network.moveTo({ scale: scale });
    }
}

function zoomOut() {
    if (network) {
        const scale = network.getScale() * 0.8;
        network.moveTo({ scale: scale });
    }
}

function fitGraph() {
    if (network) {
        network.fit();
    }
}

function refreshGraph() {
    const button = event.target;
    const hideLoading = showLoading(button);
    
    loadGraph().finally(() => {
        hideLoading();
        showAlert('Graph refreshed successfully', 'success', 2000);
    });
}

async function exportGraph() {
    try {
        if (!graphData || !graphData.nodes || graphData.nodes.length === 0) {
            showAlert('No graph data to export', 'warning');
            return;
        }
        
        const exportData = {
            timestamp: new Date().toISOString(),
            nodes: graphData.nodes,
            edges: graphData.edges,
            stats: {
                node_count: graphData.nodes.length,
                edge_count: graphData.edges.length,
                entity_types: [...new Set(graphData.nodes.map(n => n.type))],
                relationship_types: [...new Set(graphData.edges.map(e => e.type))]
            }
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `knowledge-graph-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
        
        showAlert('Graph exported successfully', 'success', 2000);
        
    } catch (error) {
        console.error('Error exporting graph:', error);
        showAlert('Error exporting graph: ' + error.message, 'danger');
    }
}

function toggleFullscreen() {
    const container = document.getElementById('graph-container');
    if (document.fullscreenElement) {
        document.exitFullscreen();
    } else {
        container.requestFullscreen();
    }
}

function showGraphLoading() {
    document.getElementById('graph-loading').style.display = 'block';
    document.getElementById('graph-container').style.display = 'none';
    document.getElementById('graph-empty').style.display = 'none';
}

function hideGraphLoading() {
    document.getElementById('graph-loading').style.display = 'none';
}

function showGraphEmpty() {
    hideGraphLoading();
    document.getElementById('graph-empty').style.display = 'block';
    document.getElementById('graph-container').style.display = 'none';
}

// Initialize with empty state
showGraphEmpty();
</script>
{% endblock %}
