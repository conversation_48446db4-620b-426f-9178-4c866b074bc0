{% extends "base_modern.html" %}

{% block title %}Entities - Graphiti{% endblock %}

{% block content %}
<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Knowledge Graph Entities</h1>
        <p class="text-muted">Explore extracted entities and their relationships</p>
    </div>
    <div>
        <button class="btn btn-primary" onclick="extractEntities()">
            <i class="bi bi-plus-circle"></i> Extract Entities
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="refreshEntities()">
            <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card gradient-card stats-card">
            <div class="card-body">
                <div class="stats-number" id="total-entities">-</div>
                <div class="stats-label">
                    <i class="bi bi-diagram-3"></i> Total Entities
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card gradient-card-success stats-card">
            <div class="card-body">
                <div class="stats-number" id="entity-types">-</div>
                <div class="stats-label">
                    <i class="bi bi-tags"></i> Entity Types
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card gradient-card-warning stats-card">
            <div class="card-body">
                <div class="stats-number" id="total-relationships">-</div>
                <div class="stats-label">
                    <i class="bi bi-share"></i> Relationships
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card gradient-card-info stats-card">
            <div class="card-body">
                <div class="stats-number" id="avg-mentions">-</div>
                <div class="stats-label">
                    <i class="bi bi-chat-quote"></i> Avg Mentions
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Entity Type Distribution -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-pie-chart"></i> Entity Type Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="entity-type-chart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-trophy"></i> Top Entities</h5>
            </div>
            <div class="card-body">
                <div id="top-entities-list">
                    <div class="text-center py-4">
                        <div class="loading-spinner"></div>
                        <p class="text-muted mt-2">Loading top entities...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5><i class="bi bi-funnel"></i> Search & Filters</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" class="form-control" id="search-input" placeholder="Search entities...">
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="entity-type-filter">
                    <option value="">All Types</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="sort-by">
                    <option value="mention_count">Mentions</option>
                    <option value="name">Name</option>
                    <option value="created_at">Created</option>
                    <option value="confidence">Confidence</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="sort-order">
                    <option value="desc">Descending</option>
                    <option value="asc">Ascending</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary w-100" onclick="applyFilters()">
                    <i class="bi bi-funnel"></i> Apply
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Entities Grid -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5><i class="bi bi-grid-3x3"></i> Entity Explorer</h5>
        <div>
            <button class="btn btn-sm btn-outline-primary" onclick="toggleView('grid')">
                <i class="bi bi-grid-3x3"></i> Grid
            </button>
            <button class="btn btn-sm btn-outline-primary ms-1" onclick="toggleView('list')">
                <i class="bi bi-list"></i> List
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <!-- Loading State -->
        <div id="entities-loading" class="text-center py-5">
            <div class="loading-spinner"></div>
            <p class="text-muted mt-2">Loading entities...</p>
        </div>
        
        <!-- Grid View -->
        <div id="grid-view" style="display: none;">
            <div class="row p-3" id="entities-grid">
            </div>
        </div>
        
        <!-- List View -->
        <div id="list-view" style="display: none;">
            <div class="list-group list-group-flush" id="entities-list">
            </div>
        </div>
        
        <!-- Empty State -->
        <div id="empty-state" class="text-center py-5" style="display: none;">
            <i class="bi bi-diagram-3-fill fs-1 text-muted mb-3"></i>
            <h5>No Entities Found</h5>
            <p class="text-muted">Process some documents to extract entities</p>
            <button class="btn btn-primary" onclick="window.location.href='/enhanced-upload'">
                <i class="bi bi-plus-circle"></i> Upload Documents
            </button>
        </div>
    </div>
    
    <!-- Pagination -->
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <small class="text-muted">
                    Showing <span id="showing-start">0</span> to <span id="showing-end">0</span> 
                    of <span id="total-count">0</span> entities
                </small>
            </div>
            <nav>
                <ul class="pagination pagination-sm mb-0" id="pagination">
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Entity Details Modal -->
<div class="modal fade" id="entity-details-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Entity Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="entity-details-content">
                <div class="text-center py-4">
                    <div class="loading-spinner"></div>
                    <p class="text-muted mt-2">Loading entity details...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="explore-relationships-btn">
                    <i class="bi bi-share"></i> Explore Relationships
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentLimit = 24;
let currentView = 'grid';
let entities = [];
let entityTypes = [];
let selectedEntityId = null;

document.addEventListener('DOMContentLoaded', function() {
    loadEntities();
    loadEntityTypes();
    setupEventListeners();
});

function setupEventListeners() {
    // Search input with debounce
    let searchTimeout;
    document.getElementById('search-input').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            currentPage = 1;
            loadEntities();
        }, 500);
    });
    
    // Sort change handlers
    document.getElementById('sort-by').addEventListener('change', () => {
        currentPage = 1;
        loadEntities();
    });
    
    document.getElementById('sort-order').addEventListener('change', () => {
        currentPage = 1;
        loadEntities();
    });
    
    document.getElementById('entity-type-filter').addEventListener('change', () => {
        currentPage = 1;
        loadEntities();
    });
}

async function loadEntities() {
    try {
        showLoading();
        
        const searchTerm = document.getElementById('search-input').value;
        const sortBy = document.getElementById('sort-by').value;
        const sortOrder = document.getElementById('sort-order').value;
        const entityType = document.getElementById('entity-type-filter').value;
        
        const params = new URLSearchParams({
            page: currentPage,
            limit: currentLimit,
            sort_by: sortBy,
            sort_order: sortOrder
        });
        
        if (searchTerm) params.append('search', searchTerm);
        if (entityType) params.append('entity_type', entityType);
        
        console.log('Fetching entities from:', `/api/entities?${params}`);
        const response = await fetch(`/api/entities?${params}`);
        if (!response.ok) throw new Error('Failed to load entities');

        const data = await response.json();
        console.log('Entities API response:', data);
        console.log('Entities count:', data.entities ? data.entities.length : 0);

        entities = data.entities || [];

        updateStats(data);
        displayEntities(entities);
        updatePagination(data.pagination || {});
        
    } catch (error) {
        console.error('Error loading entities:', error);
        showAlert('Error loading entities: ' + error.message, 'danger');
        showEmptyState();
    }
}

async function loadEntityTypes() {
    try {
        const response = await fetch('/api/entity-types');
        if (response.ok) {
            const data = await response.json();
            entityTypes = data.entity_types || [];

            // Populate filter dropdown
            const filterSelect = document.getElementById('entity-type-filter');
            filterSelect.innerHTML = '<option value="">All Types</option>';

            entityTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type;
                option.textContent = type;
                filterSelect.appendChild(option);
            });

            // Chart will be created from updateStats() using type counts
        }
    } catch (error) {
        console.error('Error loading entity types:', error);
    }
}

function updateStats(data) {
    document.getElementById('total-entities').textContent = data.total_count || 0;

    // Use actual type count from type_counts if available
    const typeCount = data.type_counts ? Object.keys(data.type_counts).filter(type => type && type !== '').length : entityTypes.length;
    document.getElementById('entity-types').textContent = typeCount || 0;

    document.getElementById('total-relationships').textContent = data.total_relationships || 0;

    const avgMentions = entities.length > 0
        ? (entities.reduce((sum, e) => sum + (e.mention_count || 0), 0) / entities.length).toFixed(1)
        : 0;
    document.getElementById('avg-mentions').textContent = avgMentions;

    // Update chart with type counts if available
    if (data.type_counts) {
        createEntityTypeChartFromCounts(data.type_counts);
    }
}

function displayEntities(ents) {
    console.log('displayEntities called with:', ents ? ents.length : 0, 'entities');
    console.log('Current view:', currentView);

    hideLoading();

    if (!ents || ents.length === 0) {
        console.log('No entities to display, showing empty state');
        showEmptyState();
        return;
    }

    console.log('Displaying entities in', currentView, 'view');

    if (currentView === 'grid') {
        displayGridView(ents);
    } else {
        displayListView(ents);
    }

    loadTopEntities();
}

function displayGridView(ents) {
    console.log('Displaying grid view with', ents.length, 'entities');

    const grid = document.getElementById('entities-grid');
    if (!grid) {
        console.error('entities-grid element not found');
        return;
    }

    grid.innerHTML = '';

    ents.forEach((entity, index) => {
        console.log(`Creating card for entity ${index + 1}:`, entity.name, entity.type);

        const card = document.createElement('div');
        card.className = 'col-md-4 col-lg-3 mb-3';
        card.innerHTML = `
            <div class="card h-100 fade-in entity-card" onclick="viewEntity('${entity.uuid}')">
                <div class="card-body text-center">
                    <div class="mb-3">
                        ${getEntityTypeIcon(entity.type)}
                    </div>
                    <h6 class="card-title">${formatEntityName(entity.name)}</h6>
                    <div class="mb-2">
                        <span class="badge bg-primary">${entity.type || 'Unknown'}</span>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="bi bi-chat-quote"></i> ${entity.mention_count || 0} mentions
                        </small>
                    </div>
                    ${entity.confidence ? `
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar" style="width: ${entity.confidence * 100}%"></div>
                        </div>
                        <small class="text-muted">${Math.round(entity.confidence * 100)}% confidence</small>
                    ` : ''}
                </div>
            </div>
        `;
        grid.appendChild(card);
    });

    console.log('Grid populated, showing grid view');
    document.getElementById('grid-view').style.display = 'block';
    document.getElementById('list-view').style.display = 'none';
    document.getElementById('empty-state').style.display = 'none';
}

function displayListView(ents) {
    const list = document.getElementById('entities-list');
    list.innerHTML = '';
    
    ents.forEach(entity => {
        const item = document.createElement('div');
        item.className = 'list-group-item list-group-item-action fade-in';
        item.onclick = () => viewEntity(entity.uuid);
        item.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        ${getEntityTypeIcon(entity.type)}
                    </div>
                    <div>
                        <h6 class="mb-1">${formatEntityName(entity.name)}</h6>
                        <small class="text-muted">
                            <span class="badge bg-primary me-2">${entity.type || 'Unknown'}</span>
                            <i class="bi bi-chat-quote"></i> ${entity.mention_count || 0} mentions
                        </small>
                    </div>
                </div>
                <div class="text-end">
                    ${entity.confidence ? `
                        <div class="progress mb-1" style="width: 100px; height: 4px;">
                            <div class="progress-bar" style="width: ${entity.confidence * 100}%"></div>
                        </div>
                        <small class="text-muted">${Math.round(entity.confidence * 100)}%</small>
                    ` : ''}
                </div>
            </div>
        `;
        list.appendChild(item);
    });
    
    document.getElementById('list-view').style.display = 'block';
    document.getElementById('grid-view').style.display = 'none';
}

function formatEntityName(name) {
    if (!name) return 'Unknown';

    // Convert LaTeX-style notation to Unicode
    const replacements = {
        '$eta$': 'β',
        '$alpha$': 'α',
        '$beta$': 'β',
        '$gamma$': 'γ',
        '$delta$': 'δ',
        '$epsilon$': 'ε',
        '$zeta$': 'ζ',
        '$theta$': 'θ',
        '$lambda$': 'λ',
        '$mu$': 'μ',
        '$nu$': 'ν',
        '$pi$': 'π',
        '$rho$': 'ρ',
        '$sigma$': 'σ',
        '$tau$': 'τ',
        '$phi$': 'φ',
        '$chi$': 'χ',
        '$psi$': 'ψ',
        '$omega$': 'ω'
    };

    let formattedName = name;
    for (const [latex, unicode] of Object.entries(replacements)) {
        formattedName = formattedName.replace(new RegExp(latex.replace('$', '\\$'), 'gi'), unicode);
    }

    return formattedName;
}

function getEntityTypeIcon(type) {
    const iconMap = {
        'Person': '<i class="bi bi-person-circle fs-1 text-primary"></i>',
        'Organization': '<i class="bi bi-building fs-1 text-success"></i>',
        'Location': '<i class="bi bi-geo-alt fs-1 text-warning"></i>',
        'Disease': '<i class="bi bi-heart-pulse fs-1 text-danger"></i>',
        'Medication': '<i class="bi bi-capsule fs-1 text-info"></i>',
        'Nutrient': '<i class="bi bi-apple fs-1 text-success"></i>',
        'Food': '<i class="bi bi-egg-fried fs-1 text-warning"></i>',
        'Research': '<i class="bi bi-journal-medical fs-1 text-primary"></i>',
        'Process': '<i class="bi bi-gear fs-1 text-secondary"></i>',
        'Symptom': '<i class="bi bi-thermometer fs-1 text-danger"></i>',
        'Treatment': '<i class="bi bi-bandaid fs-1 text-success"></i>',
        'Bioactive_Compound': '<i class="bi bi-capsule fs-1 text-info"></i>',
        'Dosage': '<i class="bi bi-calculator fs-1 text-warning"></i>'
    };

    return iconMap[type] || '<i class="bi bi-tag fs-1 text-secondary"></i>';
}

async function loadTopEntities() {
    try {
        const response = await fetch('/api/fast/entities?limit=5');
        if (response.ok) {
            const data = await response.json();
            const topEntities = data.entities || [];
            
            const container = document.getElementById('top-entities-list');
            
            if (topEntities.length > 0) {
                container.innerHTML = topEntities.map(entity => `
                    <div class="d-flex justify-content-between align-items-center mb-3 slide-in">
                        <div class="d-flex align-items-center">
                            <div class="me-2">
                                <span class="badge bg-primary">${entity.type || 'Unknown'}</span>
                            </div>
                            <div>
                                <h6 class="mb-0">${entity.name || 'Unknown'}</h6>
                                <small class="text-muted">${entity.mention_count || 0} mentions</small>
                            </div>
                        </div>
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<p class="text-muted text-center">No entities found</p>';
            }
        }
    } catch (error) {
        console.error('Error loading top entities:', error);
    }
}

function createEntityTypeChart(types) {
    const ctx = document.getElementById('entity-type-chart').getContext('2d');

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: types.map(t => t.name),
            datasets: [{
                data: types.map(t => t.count),
                backgroundColor: [
                    '#667eea', '#764ba2', '#f093fb', '#f5576c',
                    '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
                    '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

let entityChart = null; // Global variable to store chart instance

function createEntityTypeChartFromCounts(typeCounts) {
    try {
        console.log('Creating entity type chart with counts:', typeCounts);

        const canvas = document.getElementById('entity-type-chart');
        if (!canvas) {
            console.error('Chart canvas not found');
            return;
        }

        const ctx = canvas.getContext('2d');

        // Destroy existing chart if it exists
        if (entityChart) {
            entityChart.destroy();
        }

        // Convert type counts object to sorted array
        const sortedTypes = Object.entries(typeCounts)
            .filter(([type, count]) => type && type !== '' && count > 0) // Filter out empty types
            .sort((a, b) => b[1] - a[1]) // Sort by count descending
            .slice(0, 12); // Take top 12 types for readability

        console.log('Sorted types for chart:', sortedTypes);

        if (sortedTypes.length === 0) {
            console.warn('No valid types found for chart');
            return;
        }

        const labels = sortedTypes.map(([type, count]) => type);
        const data = sortedTypes.map(([type, count]) => count);

        console.log('Chart labels:', labels);
        console.log('Chart data:', data);

        entityChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: [
                        '#667eea', '#764ba2', '#f093fb', '#f5576c',
                        '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
                        '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                onClick: (event, elements) => {
                    if (elements.length > 0) {
                        const elementIndex = elements[0].index;
                        const entityType = labels[elementIndex];
                        console.log('Chart clicked, filtering by type:', entityType);
                        filterByEntityType(entityType);
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        onClick: (event, legendItem) => {
                            const entityType = legendItem.text;
                            console.log('Legend clicked, filtering by type:', entityType);
                            filterByEntityType(entityType);
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${value} (${percentage}%) - Click to filter`;
                            }
                        }
                    }
                }
            }
        });

        console.log('Chart created successfully');

    } catch (error) {
        console.error('Error creating entity type chart:', error);
    }
}

function filterByEntityType(entityType) {
    console.log('Filtering entities by type:', entityType);

    // Update the filter dropdown
    const filterSelect = document.getElementById('entity-type-filter');
    if (filterSelect) {
        filterSelect.value = entityType;
    }

    // Reset to first page
    currentPage = 1;

    // Load entities with the filter
    loadEntities();

    // Show alert
    showAlert(`Filtering by entity type: ${entityType}`, 'info', 3000);

    // Scroll to entities section
    const entitiesSection = document.getElementById('entities-grid') || document.getElementById('entities-list');
    if (entitiesSection) {
        entitiesSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
}

async function viewEntity(entityId) {
    try {
        selectedEntityId = entityId;
        const modal = new bootstrap.Modal(document.getElementById('entity-details-modal'));
        modal.show();
        
        const response = await fetch(`/api/entity/${entityId}`);
        if (!response.ok) throw new Error('Failed to load entity details');

        const entity = await response.json();
        displayEntityDetails(entity);
        
    } catch (error) {
        console.error('Error viewing entity:', error);
        showAlert('Error loading entity details: ' + error.message, 'danger');
    }
}

function displayEntityDetails(entity) {
    const content = document.getElementById('entity-details-content');
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Basic Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Name:</strong></td><td>${formatEntityName(entity.name)}</td></tr>
                    <tr><td><strong>Type:</strong></td><td><span class="badge bg-primary">${entity.type || 'Unknown'}</span></td></tr>
                    <tr><td><strong>Mentions:</strong></td><td>${entity.mention_count || 0}</td></tr>
                    <tr><td><strong>Confidence:</strong></td><td>${entity.confidence ? Math.round(entity.confidence * 100) + '%' : 'N/A'}</td></tr>
                    <tr><td><strong>Created:</strong></td><td>${entity.created_at ? formatDate(entity.created_at) : 'Unknown'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Relationships</h6>
                <div id="entity-relationships">
                    <div class="text-center py-3">
                        <div class="loading-spinner"></div>
                        <p class="text-muted mt-2">Loading relationships...</p>
                    </div>
                </div>
            </div>
        </div>
        ${entity.description ? `
            <div class="mt-3">
                <h6>Description</h6>
                <p class="text-muted">${entity.description}</p>
            </div>
        ` : ''}
        ${entity.attributes ? `
            <div class="mt-3">
                <h6>Attributes</h6>
                <pre class="bg-light p-3 rounded">${JSON.stringify(entity.attributes, null, 2)}</pre>
            </div>
        ` : ''}
    `;
    
    // Load relationships
    loadEntityRelationships(entity.uuid);
}

async function loadEntityRelationships(entityId) {
    try {
        const response = await fetch(`/api/entity/${entityId}/relationships`);
        if (response.ok) {
            const data = await response.json();
            const relationships = data.relationships || [];
            
            const container = document.getElementById('entity-relationships');
            
            if (relationships.length > 0) {
                container.innerHTML = relationships.map(rel => `
                    <div class="mb-2">
                        <span class="badge bg-secondary">${rel.type}</span>
                        <span class="ms-2">${rel.target_name}</span>
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<p class="text-muted">No relationships found</p>';
            }
        }
    } catch (error) {
        console.error('Error loading relationships:', error);
        document.getElementById('entity-relationships').innerHTML = '<p class="text-danger">Error loading relationships</p>';
    }
}

function toggleView(view) {
    currentView = view;
    displayEntities(entities);
}

function applyFilters() {
    currentPage = 1;
    loadEntities();
}

function refreshEntities() {
    const button = event.target;
    const hideLoading = showLoading(button);
    
    loadEntities().finally(() => {
        hideLoading();
        showAlert('Entities refreshed successfully', 'success', 2000);
    });
}

async function extractEntities() {
    try {
        const response = await fetch('/api/entities/extract', {
            method: 'POST'
        });
        
        if (response.ok) {
            showAlert('Entity extraction started in background', 'success');
            setTimeout(() => {
                loadEntities();
            }, 2000);
        } else {
            throw new Error('Failed to start entity extraction');
        }
    } catch (error) {
        console.error('Error extracting entities:', error);
        showAlert('Error starting entity extraction: ' + error.message, 'danger');
    }
}

function updatePagination(pagination) {
    const paginationEl = document.getElementById('pagination');
    const totalPages = pagination.total_pages || 1;
    
    // Update showing info
    document.getElementById('showing-start').textContent = ((currentPage - 1) * currentLimit) + 1;
    document.getElementById('showing-end').textContent = Math.min(currentPage * currentLimit, pagination.total_count || 0);
    document.getElementById('total-count').textContent = pagination.total_count || 0;
    
    // Generate pagination
    paginationEl.innerHTML = '';
    
    if (totalPages <= 1) return;
    
    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">Previous</a>`;
    paginationEl.appendChild(prevLi);
    
    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        paginationEl.appendChild(li);
    }
    
    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">Next</a>`;
    paginationEl.appendChild(nextLi);
}

function changePage(page) {
    if (page < 1) return;
    currentPage = page;
    loadEntities();
}

function showLoading() {
    document.getElementById('entities-loading').style.display = 'block';
    document.getElementById('grid-view').style.display = 'none';
    document.getElementById('list-view').style.display = 'none';
    document.getElementById('empty-state').style.display = 'none';
}

function hideLoading() {
    document.getElementById('entities-loading').style.display = 'none';
}

function showEmptyState() {
    hideLoading();
    document.getElementById('empty-state').style.display = 'block';
    document.getElementById('grid-view').style.display = 'none';
    document.getElementById('list-view').style.display = 'none';
}

// Add CSS for entity cards
const style = document.createElement('style');
style.textContent = `
    .entity-card {
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .entity-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
