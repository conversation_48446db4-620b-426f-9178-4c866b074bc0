"""
Fast API routes for quick UI loading - simplified versions of complex endpoints
"""

from fastapi import APIRouter, HTTPException, Query
from typing import Dict, Any, Optional
import logging

from database.database_service import get_falkordb_adapter
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api/fast", tags=["fast"])

@router.get("/graph-stats")
async def get_fast_graph_stats():
    """
    Get basic graph statistics quickly.
    """
    try:
        adapter = await get_falkordb_adapter()
        
        # Simple count queries without complex processing
        doc_query = "MATCH (d:Episode) RETURN count(d) as count"
        doc_result = adapter.execute_cypher(doc_query)
        document_count = doc_result[1][0][0] if len(doc_result) > 1 and len(doc_result[1]) > 0 else 0
        
        entity_query = "MATCH (e:Entity) RETURN count(e) as count"
        entity_result = adapter.execute_cypher(entity_query)
        entity_count = entity_result[1][0][0] if len(entity_result) > 1 and len(entity_result[1]) > 0 else 0
        
        return {
            "total_episodes": document_count,
            "total_entities": entity_count,
            "total_relationships": 0,  # Skip for speed
            "entity_type_count": 0,    # Skip for speed
            "status": "fast_mode"
        }
        
    except Exception as e:
        logger.error(f"Error getting fast graph stats: {str(e)}")
        return {
            "total_episodes": 0,
            "total_entities": 0,
            "total_relationships": 0,
            "entity_type_count": 0,
            "status": "error",
            "error": str(e)
        }

@router.get("/entities")
async def get_fast_entities(
    limit: int = Query(50, ge=1, le=100),  # Much smaller limit
    entity_type: Optional[str] = None
):
    """
    Get entities quickly with minimal processing.
    """
    try:
        adapter = await get_falkordb_adapter()

        # Simple query without complex joins
        if entity_type:
            query = f"""
            MATCH (e:Entity)
            WHERE e.type = '{entity_type}'
            RETURN e.uuid as uuid, e.name as name, e.type as type
            LIMIT {limit}
            """
        else:
            query = f"""
            MATCH (e:Entity)
            RETURN e.uuid as uuid, e.name as name, e.type as type
            LIMIT {limit}
            """

        result = adapter.execute_cypher(query)

        entities_list = []
        if result and len(result) > 1:
            for row in result[1]:
                try:
                    # FalkorDB returns simple values for property queries
                    uuid_val = str(row[0]) if row[0] is not None else ""
                    name_val = str(row[1]) if row[1] is not None else "Unknown"
                    type_val = str(row[2]) if row[2] is not None else "Unknown"

                    entities_list.append({
                        "uuid": uuid_val,
                        "name": name_val,
                        "type": type_val,
                        "mention_count": 1  # Default value for speed
                    })
                except Exception as e:
                    logger.warning(f"Error processing entity row: {str(e)}")
                    continue

        return {
            "entities": entities_list,
            "count": len(entities_list),
            "entity_type": entity_type or "all",
            "status": "fast_mode"
        }

    except Exception as e:
        logger.error(f"Error getting fast entities: {str(e)}")
        return {
            "entities": [],
            "count": 0,
            "entity_type": entity_type or "all",
            "status": "error",
            "error": str(e)
        }

@router.get("/documents")
async def get_fast_documents(limit: int = Query(20, ge=1, le=50)):
    """
    Get documents quickly.
    """
    try:
        adapter = await get_falkordb_adapter()
        
        query = f"""
        MATCH (d:Episode)
        RETURN d.uuid as uuid, d.name as name, d.created_at as created_at
        LIMIT {limit}
        """
        
        result = adapter.execute_cypher(query)
        
        documents_list = []
        if result and len(result) > 1:
            for row in result[1]:
                try:
                    # FalkorDB returns simple values for property queries
                    uuid_val = str(row[0]) if row[0] is not None else ""
                    name_val = str(row[1]) if row[1] is not None else "Unknown"
                    created_at_val = row[2] if row[2] is not None else None

                    # Convert timestamp if it's a number
                    if isinstance(created_at_val, (int, float)):
                        from datetime import datetime
                        created_at_val = datetime.fromtimestamp(created_at_val / 1000).isoformat()
                    elif created_at_val:
                        created_at_val = str(created_at_val)
                    else:
                        created_at_val = ""
                    
                    documents_list.append({
                        "uuid": uuid_val,
                        "name": name_val,
                        "created_at": created_at_val,
                        "entity_count": 0,  # Skip for speed
                        "chunk_count": 0    # Skip for speed
                    })
                except Exception as e:
                    logger.warning(f"Error processing document row: {str(e)}")
                    continue
        
        return {
            "documents": documents_list,
            "count": len(documents_list),
            "status": "fast_mode"
        }
        
    except Exception as e:
        logger.error(f"Error getting fast documents: {str(e)}")
        return {
            "documents": [],
            "count": 0,
            "status": "error",
            "error": str(e)
        }

@router.get("/references")
async def get_fast_references():
    """
    Get reference count quickly.
    """
    try:
        from pathlib import Path
        
        # Quick check of references CSV file
        csv_file = Path("references/all_references.csv")
        if csv_file.exists():
            with open(csv_file, 'r', encoding='utf-8') as f:
                line_count = sum(1 for line in f) - 1  # Subtract header
            
            return {
                "references": [],
                "count": line_count,
                "status": "fast_mode"
            }
        else:
            return {
                "references": [],
                "count": 0,
                "status": "no_file"
            }
            
    except Exception as e:
        logger.error(f"Error getting fast references: {str(e)}")
        return {
            "references": [],
            "count": 0,
            "status": "error",
            "error": str(e)
        }

@router.get("/health")
async def health_check():
    """
    Simple health check endpoint.
    """
    return {
        "status": "healthy",
        "message": "Fast API routes are working"
    }
